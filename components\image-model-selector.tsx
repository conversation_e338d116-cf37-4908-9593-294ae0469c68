"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ImageIcon, CheckCircle, AlertTriangle, Loader2, Info } from "lucide-react"
import {
  getImageModelSelectOptions,
  getImageModelInfo,
  isValidImageModel,
  type ImageModelSelectOption,
} from "@/lib/image-generation-models"
import { toast } from "sonner"

interface ImageModelSelectorProps {
  currentModel?: string
  onModelChange: (modelId: string) => Promise<{ success: boolean; error?: string }>
  loading?: boolean
  disabled?: boolean
}

export function ImageModelSelector({
  currentModel,
  onModelChange,
  loading = false,
  disabled = false,
}: Readonly<ImageModelSelectorProps>) {
  const [selectedModel, setSelectedModel] = useState(currentModel || "")
  const [saving, setSaving] = useState(false)
  const [modelOptions, setModelOptions] = useState<ImageModelSelectOption[]>([])
  const [loadingModels, setLoadingModels] = useState(true)

  const currentModelInfo = getImageModelInfo(selectedModel)

  // Load available models
  useEffect(() => {
    const loadModels = async () => {
      setLoadingModels(true)
      try {
        // Get available image models
        const options = getImageModelSelectOptions()
        setModelOptions(options)
      } catch (error) {
        console.error("Failed to load image models:", error)
        toast.error("Lỗi", {
          description: "Không thể tải danh sách mô hình tạo ảnh.",
        })
      } finally {
        setLoadingModels(false)
      }
    }

    loadModels()
  }, [])

  // Update selected model when currentModel prop changes
  useEffect(() => {
    if (currentModel !== selectedModel) {
      setSelectedModel(currentModel || "")
    }
  }, [currentModel, selectedModel])

  const handleModelChange = async (modelId: string) => {
    if (modelId === selectedModel) return

    setSelectedModel(modelId)
    
    if (!modelId) return

    setSaving(true)
    try {
      const result = await onModelChange(modelId)

      if (!result.success) {
        toast.error("Lỗi Lưu", {
          description: result.error || "Không thể lưu lựa chọn mô hình tạo ảnh.",
        })
        // Revert selection on failure
        setSelectedModel(currentModel || "")
      } else {
        const modelInfo = getImageModelInfo(modelId)
        toast.success("Đã Cập Nhật Mô Hình", {
          description: `Đã chuyển sang ${modelInfo?.name || modelId} để tạo ảnh`,
        })
      }
    } catch (error: unknown) {
      toast.error("Lỗi", {
        description: error instanceof Error ? error.message : "Không thể lưu lựa chọn mô hình tạo ảnh.",
      })
      // Revert selection on failure
      setSelectedModel(currentModel || "")
    } finally {
      setSaving(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ImageIcon className="w-5 h-5" />
          Mô Hình AI Tạo Ảnh
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Model Selection */}
        <div className="space-y-2">
          <Label htmlFor="image-model">Mô Hình Tạo Ảnh</Label>
          <Select
            value={selectedModel}
            onValueChange={handleModelChange}
            disabled={loading || saving || disabled || loadingModels}
          >
            <SelectTrigger id="image-model">
              <SelectValue placeholder={loadingModels ? "Đang tải mô hình..." : "Chọn mô hình tạo ảnh"} />
            </SelectTrigger>
            <SelectContent>
              {loadingModels ? (
                <SelectItem value="loading" disabled>
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Đang tải mô hình...
                  </div>
                </SelectItem>
              ) : modelOptions.length > 0 ? (
                modelOptions.map((option: ImageModelSelectOption) => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    disabled={option.disabled}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span>{option.label}</span>
                          <Badge variant="secondary" className="text-xs">
                            {option.provider}
                          </Badge>
                          {option.badge && (
                            <Badge variant="default" className="text-xs">
                              {option.badge}
                            </Badge>
                          )}
                        </div>
                        <span className="text-xs text-gray-500 mt-1">
                          {option.description}
                        </span>
                      </div>
                    </div>
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="no-models" disabled>
                  Không có mô hình nào khả dụng
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Model Information */}
        {currentModelInfo && (
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-green-600">Mô hình đã được chọn</span>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-3 space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Nhà cung cấp:</span>
                <Badge variant="outline">{currentModelInfo.provider.toUpperCase()}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Độ phân giải tối đa:</span>
                <span className="text-sm text-gray-600">{currentModelInfo.maxResolution}</span>
              </div>
              <div className="flex items-start justify-between">
                <span className="text-sm font-medium">Tỷ lệ khung hình:</span>
                <div className="flex flex-wrap gap-1 max-w-[200px]">
                  {currentModelInfo.aspectRatios.map((ratio) => (
                    <Badge key={ratio} variant="secondary" className="text-xs">
                      {ratio}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="space-y-1">
                <span className="text-sm font-medium">Khả năng:</span>
                <div className="flex flex-wrap gap-1">
                  {currentModelInfo.capabilities.textToImage && (
                    <Badge variant="outline" className="text-xs">Văn bản → Ảnh</Badge>
                  )}
                  {currentModelInfo.capabilities.imageToImage && (
                    <Badge variant="outline" className="text-xs">Ảnh → Ảnh</Badge>
                  )}
                  {currentModelInfo.capabilities.inpainting && (
                    <Badge variant="outline" className="text-xs">Chỉnh sửa</Badge>
                  )}
                  {currentModelInfo.capabilities.outpainting && (
                    <Badge variant="outline" className="text-xs">Mở rộng</Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Information Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Mô hình tạo ảnh được sử dụng để tạo hình ảnh cho thực phẩm và công thức. 
            Các mô hình khác nhau có chất lượng và chi phí khác nhau.
          </AlertDescription>
        </Alert>

        {/* API Key Required Warning */}
        {currentModelInfo?.requiresApiKey && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Mô hình này yêu cầu API key riêng từ {currentModelInfo.provider.toUpperCase()}. 
              Vui lòng cấu hình API key trong phần cài đặt tương ứng.
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {(loading || saving) && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>{saving ? "Đang lưu..." : "Đang tải..."}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
