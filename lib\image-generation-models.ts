/**
 * Image Generation AI Model Configuration
 * Defines available image generation models, their capabilities, and configuration options
 */

export interface ImageModelInfo {
  id: string
  name: string
  description: string
  provider: 'openai' | 'stability' | 'midjourney' | 'replicate'
  capabilities: {
    textToImage: boolean
    imageToImage: boolean
    inpainting: boolean
    outpainting: boolean
  }
  maxResolution: string
  aspectRatios: string[]
  isRecommended?: boolean
  isAvailable?: boolean
  requiresApiKey?: boolean
}

export interface ImageModelSelectOption {
  value: string
  label: string
  description: string
  provider: string
  badge?: string
  disabled?: boolean
}

/**
 * Predefined image generation models
 */
export const IMAGE_GENERATION_MODELS: ImageModelInfo[] = [
  // OpenAI DALL-E Models
  {
    id: 'dall-e-3',
    name: 'DALL-E 3',
    description: 'Mô hình tạo ảnh tiên tiến nhất của OpenAI với chất lượng cao và hiểu ngữ cảnh tốt',
    provider: 'openai',
    capabilities: {
      textToImage: true,
      imageToImage: false,
      inpainting: false,
      outpainting: false,
    },
    maxResolution: '1024x1024',
    aspectRatios: ['1:1', '16:9', '9:16'],
    isRecommended: true,
    isAvailable: true,
    requiresApiKey: true,
  },
  {
    id: 'dall-e-2',
    name: 'DALL-E 2',
    description: 'Mô hình tạo ảnh cũ hơn của OpenAI, chi phí thấp hơn nhưng chất lượng kém hơn',
    provider: 'openai',
    capabilities: {
      textToImage: true,
      imageToImage: true,
      inpainting: true,
      outpainting: true,
    },
    maxResolution: '1024x1024',
    aspectRatios: ['1:1'],
    isRecommended: false,
    isAvailable: true,
    requiresApiKey: true,
  },
  // Stability AI Models
  {
    id: 'stable-diffusion-xl',
    name: 'Stable Diffusion XL',
    description: 'Mô hình mã nguồn mở mạnh mẽ với khả năng tùy chỉnh cao',
    provider: 'stability',
    capabilities: {
      textToImage: true,
      imageToImage: true,
      inpainting: true,
      outpainting: false,
    },
    maxResolution: '1024x1024',
    aspectRatios: ['1:1', '16:9', '9:16', '4:3', '3:4'],
    isRecommended: true,
    isAvailable: true,
    requiresApiKey: true,
  },
  {
    id: 'stable-diffusion-3',
    name: 'Stable Diffusion 3',
    description: 'Phiên bản mới nhất của Stable Diffusion với hiệu suất được cải thiện',
    provider: 'stability',
    capabilities: {
      textToImage: true,
      imageToImage: true,
      inpainting: true,
      outpainting: false,
    },
    maxResolution: '1024x1024',
    aspectRatios: ['1:1', '16:9', '9:16', '4:3', '3:4'],
    isRecommended: true,
    isAvailable: true,
    requiresApiKey: true,
  },
  // Midjourney (via API when available)
  {
    id: 'midjourney-v6',
    name: 'Midjourney v6',
    description: 'Mô hình tạo ảnh nghệ thuật chất lượng cao (cần API key riêng)',
    provider: 'midjourney',
    capabilities: {
      textToImage: true,
      imageToImage: true,
      inpainting: false,
      outpainting: false,
    },
    maxResolution: '2048x2048',
    aspectRatios: ['1:1', '16:9', '9:16', '4:3', '3:4', '2:3', '3:2'],
    isRecommended: false,
    isAvailable: false, // Not yet available via API
    requiresApiKey: true,
  },
  // Replicate Models
  {
    id: 'replicate-flux',
    name: 'Flux (Replicate)',
    description: 'Mô hình tạo ảnh nhanh và chất lượng cao qua Replicate',
    provider: 'replicate',
    capabilities: {
      textToImage: true,
      imageToImage: true,
      inpainting: false,
      outpainting: false,
    },
    maxResolution: '1024x1024',
    aspectRatios: ['1:1', '16:9', '9:16', '4:3', '3:4'],
    isRecommended: true,
    isAvailable: true,
    requiresApiKey: true,
  },
]

/**
 * Get all available image generation models
 * @returns Array of available image models
 */
export function getAvailableImageModels(): ImageModelInfo[] {
  return IMAGE_GENERATION_MODELS.filter(model => model.isAvailable)
}

/**
 * Get image model by ID
 * @param modelId - Model ID to find
 * @returns Model info or undefined if not found
 */
export function getImageModelInfo(modelId: string): ImageModelInfo | undefined {
  return IMAGE_GENERATION_MODELS.find(model => model.id === modelId)
}

/**
 * Check if a model ID is valid
 * @param modelId - Model ID to validate
 * @returns Boolean indicating if model is valid
 */
export function isValidImageModel(modelId: string): boolean {
  return IMAGE_GENERATION_MODELS.some(model => model.id === modelId && model.isAvailable)
}

/**
 * Get image models by provider
 * @param provider - Provider to filter by
 * @returns Array of models from the specified provider
 */
export function getImageModelsByProvider(provider: ImageModelInfo['provider']): ImageModelInfo[] {
  return IMAGE_GENERATION_MODELS.filter(model => model.provider === provider && model.isAvailable)
}

/**
 * Get image model options formatted for Select components
 * @returns Array of formatted model options
 */
export function getImageModelSelectOptions(): ImageModelSelectOption[] {
  return getAvailableImageModels()
    .map(model => ({
      value: model.id,
      label: model.name,
      description: model.description,
      provider: model.provider.toUpperCase(),
      badge: model.isRecommended ? 'Khuyến nghị' : undefined,
      disabled: !model.isAvailable,
    }))
    .sort((a, b) => {
      // Sort recommended models first
      if (a.badge && !b.badge) return -1
      if (!a.badge && b.badge) return 1
      // Then sort by provider
      if (a.provider !== b.provider) return a.provider.localeCompare(b.provider)
      // Finally sort by name
      return a.label.localeCompare(b.label)
    })
}

/**
 * Get recommended image model
 * @returns ID of the recommended model or null if none available
 */
export function getRecommendedImageModel(): string | null {
  const recommended = IMAGE_GENERATION_MODELS.find(model => 
    model.isRecommended && model.isAvailable
  )
  return recommended?.id || null
}
